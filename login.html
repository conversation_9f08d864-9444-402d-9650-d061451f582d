<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - الكلية التربوية المفتوحة</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #c41e3a 75%, #8b0000 100%);
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(196, 30, 58, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            padding: 45px;
            width: 100%;
            max-width: 420px;
            border: 2px solid rgba(196, 30, 58, 0.1);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72, #2a5298, #c41e3a, #8b0000);
        }

        .login-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .login-header h2 {
            color: #1e3c72;
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 28px;
        }

        .login-header .college-name {
            color: #c41e3a;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            direction: ltr;
            text-align: left;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-floating > .form-control:focus {
            border-color: #c41e3a;
            box-shadow: 0 0 0 0.2rem rgba(196, 30, 58, 0.25);
            background: rgba(255, 255, 255, 1);
        }

        .form-floating > .form-control:hover {
            border-color: #2a5298;
            background: rgba(255, 255, 255, 1);
        }

        .form-floating > label {
            color: #1e3c72;
            font-weight: 500;
            direction: rtl;
        }

        .btn-login {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #c41e3a 100%);
            border: none;
            border-radius: 15px;
            padding: 14px;
            font-size: 17px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            color: white;
            box-shadow: 0 4px 15px rgba(196, 30, 58, 0.3);
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(196, 30, 58, 0.4);
            background: linear-gradient(135deg, #c41e3a 0%, #2a5298 50%, #1e3c72 100%);
            color: white;
        }

        .btn-login:active {
            transform: translateY(-1px);
        }

        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-password a {
            color: #2a5298;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
            font-weight: 500;
        }

        .forgot-password a:hover {
            color: #c41e3a;
        }

        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }

        .social-login {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-social {
            flex: 1;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: white;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-social:hover {
            border-color: #c41e3a;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(196, 30, 58, 0.2);
        }

        .signup-link {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .signup-link a {
            color: #2a5298;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            color: #c41e3a;
        }

        @media (max-width: 576px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }

            body {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="bi bi-mortarboard-fill"></i> تسجيل الدخول</h2>
            <div class="college-name">الكلية التربوية المفتوحة - العراق</div>
            <p>مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك</p>
        </div>

        <form id="loginForm" action="#" method="POST">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" dir="rtlr" required>
                <label for="username"><i class="bi bi-person"></i> اسم المستخدم</label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" dir="rtlr" required>
                <label for="password"><i class="bi bi-lock"></i> كلمة المرور</label>
            </div>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    تذكرني
                </label>
            </div>

            <button type="submit" class="btn btn-login">
                <i class="bi bi-box-arrow-in-right"></i> تسجيل الدخول
            </button>
        </form>

        <div class="forgot-password">
            <a href="#" onclick="alert('سيتم توجيهك لصفحة استعادة كلمة المرور')">نسيت كلمة المرور؟</a>
        </div>

        <div class="divider">
            <span>أو</span>
        </div>

        <div class="social-login">
            <button class="btn btn-social" onclick="alert('تسجيل الدخول عبر Google')">
                <i class="bi bi-google" style="color: #db4437;"></i>
                Google
            </button>
            <button class="btn btn-social" onclick="alert('تسجيل الدخول عبر Facebook')">
                <i class="bi bi-facebook" style="color: #4267B2;"></i>
                Facebook
            </button>
        </div>

        <div class="signup-link">
            <span>ليس لديك حساب؟ </span>
            <a href="#" onclick="alert('سيتم توجيهك لصفحة إنشاء حساب جديد')">إنشاء حساب جديد</a>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // معالج إرسال النموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
                // هنا يمكنك إضافة كود الاتصال بالخادم (Backend)
                alert('تم إرسال بيانات تسجيل الدخول:\nاسم المستخدم: ' + username + '\nكلمة المرور: ' + password);

                // مثال على إرسال البيانات للخادم
                /*
                fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/dashboard';
                    } else {
                        alert('خطأ في تسجيل الدخول: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال بالخادم');
                });
                */
            } else {
                alert('يرجى ملء جميع الحقول المطلوبة');
            }
        });

        // تأثيرات تفاعلية إضافية
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
