<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات الموظفين - الكلية التربوية المفتوحة</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #c41e3a 75%, #8b0000 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px 0;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(196, 30, 58, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            margin: 20px auto;
            max-width: 1200px;
            border: 2px solid rgba(196, 30, 58, 0.1);
            position: relative;
            overflow: hidden;
        }

        .main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72, #2a5298, #c41e3a, #8b0000);
        }

        .page-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #c41e3a 100%);
            color: white;
            padding: 30px;
            margin: -2px -2px 30px -2px;
            border-radius: 25px 25px 0 0;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .section-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(196, 30, 58, 0.1);
            transition: all 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .section-title {
            color: #1e3c72;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 3px solid #c41e3a;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-floating > .form-control,
        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            direction: rtl;
            text-align: right;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-floating > .form-control:focus,
        .form-select:focus {
            border-color: #c41e3a;
            box-shadow: 0 0 0 0.2rem rgba(196, 30, 58, 0.25);
            background: rgba(255, 255, 255, 1);
        }

        .form-floating > .form-control:hover,
        .form-select:hover {
            border-color: #2a5298;
            background: rgba(255, 255, 255, 1);
        }

        .form-floating > label {
            color: #1e3c72;
            font-weight: 500;
            direction: rtl;
        }

        .form-floating > .form-control::placeholder {
            direction: rtl;
            text-align: right;
            color: #999;
            font-weight: 400;
        }



        .btn-success-custom {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 15px;
            padding: 18px 50px;
            font-size: 20px;
            font-weight: 700;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            margin: 10px;
            display: inline-block;
            min-width: 250px;
        }

        .btn-success-custom:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.5);
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #c41e3a 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(196, 30, 58, 0.3);
            margin: 10px;
            display: inline-block;
            min-width: 200px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(196, 30, 58, 0.4);
            background: linear-gradient(135deg, #c41e3a 0%, #2a5298 50%, #1e3c72 100%);
            color: white;
        }

        .file-upload-area {
            border: 3px dashed #c41e3a;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            background: rgba(196, 30, 58, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #2a5298;
            background: rgba(42, 82, 152, 0.05);
        }

        .file-upload-area i {
            font-size: 3rem;
            color: #c41e3a;
            margin-bottom: 15px;
        }

        .add-employee-section {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(32, 201, 151, 0.15) 100%);
            border: 3px solid rgba(40, 167, 69, 0.3);
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
        }

        .add-employee-section h4 {
            font-size: 1.8rem;
            color: #28a745;
            margin-bottom: 20px;
        }

        .add-employee-section p {
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .salary-input {
            position: relative;
        }

        .salary-input::after {
            content: 'د.ع';
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: 500;
        }

        .search-result-card {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(42, 82, 152, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .search-result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(42, 82, 152, 0.2);
            border-color: #c41e3a;
        }

        .employee-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #c41e3a;
        }

        .info-card h6 {
            color: #1e3c72;
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #666;
        }

        .info-value {
            font-weight: 600;
            color: #333;
        }

        .document-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .document-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            min-width: 150px;
            transition: all 0.3s ease;
        }

        .document-item:hover {
            border-color: #c41e3a;
            transform: translateY(-2px);
        }

        .document-item i {
            font-size: 2rem;
            color: #c41e3a;
            margin-bottom: 10px;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 12px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 20px;
            }

            .page-header {
                padding: 20px;
                margin: -2px -2px 20px -2px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .section-card {
                padding: 20px;
                margin-bottom: 20px;
            }

            .employee-info-grid {
                grid-template-columns: 1fr;
            }

            .document-preview {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="page-header">
                <h1><i class="bi bi-people-fill"></i> بيانات الموظفين</h1>
                <div class="subtitle">الكلية التربوية المفتوحة - العراق</div>
            </div>

            <div class="container">
                <!-- Search Section -->
                <div class="section-card mb-4">
                    <h3 class="section-title">
                        <i class="bi bi-search"></i>
                        البحث عن موظف
                    </h3>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="searchEmployeeId" placeholder="رقم الموظف">
                                <label for="searchEmployeeId">رقم الموظف</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="searchEmployeeName" placeholder="اسم الموظف">
                                <label for="searchEmployeeName">اسم الموظف</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <select class="form-select" id="searchDepartment">
                                <option value="">جميع الأقسام</option>
                                <option value="admin">الإدارة</option>
                                <option value="academic">الشؤون الأكاديمية</option>
                                <option value="student">شؤون الطلاب</option>
                                <option value="finance">الشؤون المالية</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="it">تقنية المعلومات</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn btn-primary-custom" onclick="searchEmployee()">
                            <i class="bi bi-search me-2"></i>
                            البحث
                        </button>
                        <button type="button" class="btn btn-secondary ms-3" onclick="clearSearch()">
                            <i class="bi bi-x-circle me-2"></i>
                            مسح البحث
                        </button>
                    </div>
                </div>

                <!-- Search Results Section -->
                <div id="searchResults" class="section-card" style="display: none;">
                    <h3 class="section-title">
                        <i class="bi bi-person-lines-fill"></i>
                        نتائج البحث
                    </h3>
                    <div id="searchResultsContent"></div>
                </div>

                <!-- Employee Details Modal -->
                <div class="modal fade" id="employeeDetailsModal" tabindex="-1" aria-labelledby="employeeDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="employeeDetailsModalLabel">
                                    <i class="bi bi-person-badge"></i>
                                    تفاصيل الموظف
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="employeeDetailsContent">
                                <!-- Employee details will be loaded here -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="editEmployee()">تعديل البيانات</button>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="employeeForm">
                    <!-- Personal Information Section -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="bi bi-person-circle"></i>
                            المعلومات الشخصية
                        </h3>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="fullName" placeholder="الاسم الكامل" required>
                                    <label for="fullName">الاسم الكامل</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="birthDate" required>
                                    <label for="birthDate">تاريخ الميلاد</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <select class="form-select" id="gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="phone" placeholder="رقم الهاتف" required>
                                    <label for="phone">رقم الهاتف</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="email" placeholder="البريد الإلكتروني" required>
                                    <label for="email">البريد الإلكتروني</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <textarea class="form-control" id="address" placeholder="العنوان" style="height: 100px" required></textarea>
                                    <label for="address">العنوان</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Job Information Section -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="bi bi-briefcase-fill"></i>
                            المعلومات الوظيفية
                        </h3>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="employeeId" placeholder="رقم الموظف" required>
                                    <label for="employeeId">رقم الموظف</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="jobTitle" placeholder="الوظيفة" required>
                                    <label for="jobTitle">الوظيفة</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <select class="form-select" id="department" required>
                                    <option value="">اختر القسم</option>
                                    <option value="admin">الإدارة</option>
                                    <option value="academic">الشؤون الأكاديمية</option>
                                    <option value="student">شؤون الطلاب</option>
                                    <option value="finance">الشؤون المالية</option>
                                    <option value="hr">الموارد البشرية</option>
                                    <option value="it">تقنية المعلومات</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="hireDate" required>
                                    <label for="hireDate">تاريخ التعيين</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating salary-input">
                                    <input type="number" class="form-control" id="salary" placeholder="الراتب" required>
                                    <label for="salary">الراتب (دينار عراقي)</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Upload Section -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="bi bi-file-earmark-text-fill"></i>
                            المستمسكات الرسمية
                        </h3>
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <label class="form-label fw-bold text-primary">
                                    <i class="bi bi-card-heading"></i> بطاقة الهوية
                                </label>
                                <div class="file-upload-area" onclick="document.getElementById('idCard').click()">
                                    <i class="bi bi-cloud-upload"></i>
                                    <div class="fw-bold">اضغط لتحميل بطاقة الهوية</div>
                                    <small class="text-muted">PDF, JPG, PNG (حد أقصى 5MB)</small>
                                    <input type="file" id="idCard" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                </div>
                                <div id="idCardPreview" class="mt-2"></div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <label class="form-label fw-bold text-primary">
                                    <i class="bi bi-house-door"></i> بطاقة السكن
                                </label>
                                <div class="file-upload-area" onclick="document.getElementById('residenceCard').click()">
                                    <i class="bi bi-cloud-upload"></i>
                                    <div class="fw-bold">اضغط لتحميل بطاقة السكن</div>
                                    <small class="text-muted">PDF, JPG, PNG (حد أقصى 5MB)</small>
                                    <input type="file" id="residenceCard" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                </div>
                                <div id="residenceCardPreview" class="mt-2"></div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <label class="form-label fw-bold text-primary">
                                    <i class="bi bi-mortarboard"></i> شهادة التخرج
                                </label>
                                <div class="file-upload-area" onclick="document.getElementById('diploma').click()">
                                    <i class="bi bi-cloud-upload"></i>
                                    <div class="fw-bold">اضغط لتحميل شهادة التخرج</div>
                                    <small class="text-muted">PDF, JPG, PNG (حد أقصى 5MB)</small>
                                    <input type="file" id="diploma" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                </div>
                                <div id="diplomaPreview" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Additional Documents -->
                        <div class="row mt-4">
                            <div class="col-md-6 mb-4">
                                <label class="form-label fw-bold text-primary">
                                    <i class="bi bi-file-medical"></i> الفحص الطبي
                                </label>
                                <div class="file-upload-area" onclick="document.getElementById('medicalReport').click()">
                                    <i class="bi bi-cloud-upload"></i>
                                    <div class="fw-bold">اضغط لتحميل الفحص الطبي</div>
                                    <small class="text-muted">PDF, JPG, PNG (حد أقصى 5MB)</small>
                                    <input type="file" id="medicalReport" class="d-none" accept=".pdf,.jpg,.jpeg,.png">
                                </div>
                                <div id="medicalReportPreview" class="mt-2"></div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <label class="form-label fw-bold text-primary">
                                    <i class="bi bi-file-plus"></i> مستمسكات أخرى
                                </label>
                                <div class="file-upload-area" onclick="document.getElementById('otherDocs').click()">
                                    <i class="bi bi-cloud-upload"></i>
                                    <div class="fw-bold">اضغط لتحميل مستمسكات أخرى</div>
                                    <small class="text-muted">PDF, JPG, PNG (حد أقصى 5MB)</small>
                                    <input type="file" id="otherDocs" class="d-none" accept=".pdf,.jpg,.jpeg,.png" multiple>
                                </div>
                                <div id="otherDocsPreview" class="mt-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Employee Button Section -->
                    <div class="add-employee-section">
                        <h4 class="text-success mb-3">
                            <i class="bi bi-person-plus-fill"></i>
                            إضافة موظف جديد
                        </h4>
                        <p class="text-muted mb-4">تأكد من ملء جميع البيانات المطلوبة وتحميل المستمسكات الضرورية</p>
                        <button type="submit" class="btn btn-success-custom">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            حفظ بيانات الموظف
                        </button>
                        <button type="button" class="btn btn-primary-custom ms-3" onclick="resetForm()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            إعادة تعيين النموذج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sample employee data (في التطبيق الحقيقي، ستأتي هذه البيانات من قاعدة البيانات)
        const sampleEmployees = [
            {
                id: "EMP001",
                fullName: "أحمد محمد علي",
                birthDate: "1985-05-15",
                gender: "male",
                phone: "07901234567",
                email: "<EMAIL>",
                address: "بغداد - الكرادة - شارع الرشيد",
                jobTitle: "مدرس",
                department: "academic",
                hireDate: "2020-09-01",
                salary: 1500000,
                documents: {
                    idCard: "ahmed_id.pdf",
                    residenceCard: "ahmed_residence.pdf",
                    diploma: "ahmed_diploma.pdf",
                    medicalReport: "ahmed_medical.pdf"
                }
            },
            {
                id: "EMP002",
                fullName: "فاطمة حسن محمود",
                birthDate: "1990-03-22",
                gender: "female",
                phone: "07801234567",
                email: "<EMAIL>",
                address: "بغداد - الجادرية - شارع الجامعة",
                jobTitle: "موظفة إدارية",
                department: "admin",
                hireDate: "2021-01-15",
                salary: 1200000,
                documents: {
                    idCard: "fatima_id.pdf",
                    residenceCard: "fatima_residence.pdf",
                    diploma: "fatima_diploma.pdf"
                }
            },
            {
                id: "EMP003",
                fullName: "محمد عبد الله حسين",
                birthDate: "1988-11-10",
                gender: "male",
                phone: "07701234567",
                email: "<EMAIL>",
                address: "بغداد - الأعظمية - شارع الإمام الأعظم",
                jobTitle: "مطور تطبيقات",
                department: "it",
                hireDate: "2019-06-01",
                salary: 1800000,
                documents: {
                    idCard: "mohammed_id.pdf",
                    residenceCard: "mohammed_residence.pdf",
                    diploma: "mohammed_diploma.pdf",
                    medicalReport: "mohammed_medical.pdf"
                }
            }
        ];

        // Search functionality
        function searchEmployee() {
            const searchId = document.getElementById('searchEmployeeId').value.trim();
            const searchName = document.getElementById('searchEmployeeName').value.trim().toLowerCase();
            const searchDept = document.getElementById('searchDepartment').value;

            let results = sampleEmployees.filter(emp => {
                const matchId = !searchId || emp.id.toLowerCase().includes(searchId.toLowerCase());
                const matchName = !searchName || emp.fullName.toLowerCase().includes(searchName);
                const matchDept = !searchDept || emp.department === searchDept;

                return matchId && matchName && matchDept;
            });

            displaySearchResults(results);
        }

        function displaySearchResults(results) {
            const resultsContainer = document.getElementById('searchResults');
            const resultsContent = document.getElementById('searchResultsContent');

            if (results.length === 0) {
                resultsContent.innerHTML = `
                    <div class="alert alert-warning text-center">
                        <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                        <h5>لم يتم العثور على نتائج</h5>
                        <p>لم يتم العثور على موظفين يطابقون معايير البحث المحددة</p>
                    </div>
                `;
            } else {
                resultsContent.innerHTML = results.map(emp => `
                    <div class="search-result-card" onclick="showEmployeeDetails('${emp.id}')">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1 text-primary">${emp.fullName}</h6>
                                <small class="text-muted">رقم الموظف: ${emp.id}</small>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">الوظيفة:</small>
                                <div class="fw-bold">${emp.jobTitle}</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">القسم:</small>
                                <div class="fw-bold">${getDepartmentName(emp.department)}</div>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye"></i> عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            resultsContainer.style.display = 'block';
            resultsContainer.scrollIntoView({ behavior: 'smooth' });
        }

        function getDepartmentName(deptCode) {
            const departments = {
                'admin': 'الإدارة',
                'academic': 'الشؤون الأكاديمية',
                'student': 'شؤون الطلاب',
                'finance': 'الشؤون المالية',
                'hr': 'الموارد البشرية',
                'it': 'تقنية المعلومات'
            };
            return departments[deptCode] || deptCode;
        }

        function showEmployeeDetails(employeeId) {
            const employee = sampleEmployees.find(emp => emp.id === employeeId);
            if (!employee) return;

            const modalContent = document.getElementById('employeeDetailsContent');
            modalContent.innerHTML = `
                <div class="employee-info-grid">
                    <div class="info-card">
                        <h6><i class="bi bi-person-circle me-2"></i>المعلومات الشخصية</h6>
                        <div class="info-item">
                            <span class="info-label">الاسم الكامل:</span>
                            <span class="info-value">${employee.fullName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الميلاد:</span>
                            <span class="info-value">${employee.birthDate}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الجنس:</span>
                            <span class="info-value">${employee.gender === 'male' ? 'ذكر' : 'أنثى'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رقم الهاتف:</span>
                            <span class="info-value">${employee.phone}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value">${employee.email}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value">${employee.address}</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h6><i class="bi bi-briefcase-fill me-2"></i>المعلومات الوظيفية</h6>
                        <div class="info-item">
                            <span class="info-label">رقم الموظف:</span>
                            <span class="info-value">${employee.id}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الوظيفة:</span>
                            <span class="info-value">${employee.jobTitle}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">القسم:</span>
                            <span class="info-value">${getDepartmentName(employee.department)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ التعيين:</span>
                            <span class="info-value">${employee.hireDate}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الراتب:</span>
                            <span class="info-value">${employee.salary.toLocaleString()} د.ع</span>
                        </div>
                    </div>
                </div>

                <div class="info-card mt-4">
                    <h6><i class="bi bi-file-earmark-text-fill me-2"></i>المستمسكات الرسمية</h6>
                    <div class="document-preview">
                        ${employee.documents.idCard ? `
                            <div class="document-item" onclick="viewDocument('${employee.documents.idCard}')">
                                <i class="bi bi-card-heading"></i>
                                <div class="fw-bold">بطاقة الهوية</div>
                                <small class="text-muted">PDF</small>
                            </div>
                        ` : ''}
                        ${employee.documents.residenceCard ? `
                            <div class="document-item" onclick="viewDocument('${employee.documents.residenceCard}')">
                                <i class="bi bi-house-door"></i>
                                <div class="fw-bold">بطاقة السكن</div>
                                <small class="text-muted">PDF</small>
                            </div>
                        ` : ''}
                        ${employee.documents.diploma ? `
                            <div class="document-item" onclick="viewDocument('${employee.documents.diploma}')">
                                <i class="bi bi-mortarboard"></i>
                                <div class="fw-bold">شهادة التخرج</div>
                                <small class="text-muted">PDF</small>
                            </div>
                        ` : ''}
                        ${employee.documents.medicalReport ? `
                            <div class="document-item" onclick="viewDocument('${employee.documents.medicalReport}')">
                                <i class="bi bi-file-medical"></i>
                                <div class="fw-bold">الفحص الطبي</div>
                                <small class="text-muted">PDF</small>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('employeeDetailsModal'));
            modal.show();
        }

        function viewDocument(documentName) {
            alert(`عرض المستند: ${documentName}\n\nفي التطبيق الحقيقي، سيتم فتح المستند في نافذة جديدة أو تحميله.`);
        }

        function clearSearch() {
            document.getElementById('searchEmployeeId').value = '';
            document.getElementById('searchEmployeeName').value = '';
            document.getElementById('searchDepartment').value = '';
            document.getElementById('searchResults').style.display = 'none';
        }

        function editEmployee() {
            alert('سيتم توجيهك لصفحة تعديل بيانات الموظف');
        }

        // File upload preview functionality
        function setupFilePreview(inputId, previewId) {
            document.getElementById(inputId).addEventListener('change', function(e) {
                const file = e.target.files[0];
                const preview = document.getElementById(previewId);

                if (file) {
                    const fileName = file.name;
                    const fileSize = (file.size / 1024 / 1024).toFixed(2);

                    preview.innerHTML = `
                        <div class="alert alert-success d-flex align-items-center mt-2">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <div>
                                <strong>${fileName}</strong><br>
                                <small>الحجم: ${fileSize} MB</small>
                            </div>
                        </div>
                    `;
                } else {
                    preview.innerHTML = '';
                }
            });
        }

        // Setup file previews for all upload inputs
        setupFilePreview('idCard', 'idCardPreview');
        setupFilePreview('residenceCard', 'residenceCardPreview');
        setupFilePreview('diploma', 'diplomaPreview');
        setupFilePreview('medicalReport', 'medicalReportPreview');
        setupFilePreview('otherDocs', 'otherDocsPreview');

        // Form submission handler
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Collect form data
            const formData = new FormData();

            // Personal information
            formData.append('fullName', document.getElementById('fullName').value);
            formData.append('birthDate', document.getElementById('birthDate').value);
            formData.append('gender', document.getElementById('gender').value);
            formData.append('phone', document.getElementById('phone').value);
            formData.append('email', document.getElementById('email').value);
            formData.append('address', document.getElementById('address').value);

            // Job information
            formData.append('employeeId', document.getElementById('employeeId').value);
            formData.append('jobTitle', document.getElementById('jobTitle').value);
            formData.append('department', document.getElementById('department').value);
            formData.append('hireDate', document.getElementById('hireDate').value);
            formData.append('salary', document.getElementById('salary').value);

            // Files
            const fileInputs = ['idCard', 'residenceCard', 'diploma', 'medicalReport', 'otherDocs'];
            fileInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input.files.length > 0) {
                    if (inputId === 'otherDocs') {
                        for (let i = 0; i < input.files.length; i++) {
                            formData.append(inputId, input.files[i]);
                        }
                    } else {
                        formData.append(inputId, input.files[0]);
                    }
                }
            });

            // Here you would typically send the data to your backend
            alert('تم حفظ بيانات الموظف بنجاح!\n\nسيتم إرسال البيانات إلى الخادم...');

            // Example of sending to backend:
            /*
            fetch('/api/employees', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ بيانات الموظف بنجاح!');
                    resetForm();
                } else {
                    alert('حدث خطأ في حفظ البيانات: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال بالخادم');
            });
            */
        });

        // Reset form function
        function resetForm() {
            document.getElementById('employeeForm').reset();

            // Clear file previews
            const previews = ['idCardPreview', 'residenceCardPreview', 'diplomaPreview', 'medicalReportPreview', 'otherDocsPreview'];
            previews.forEach(previewId => {
                document.getElementById(previewId).innerHTML = '';
            });

            alert('تم إعادة تعيين النموذج بنجاح!');
        }

        // Add interactive effects
        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Validate file size
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', function() {
                const maxSize = 5 * 1024 * 1024; // 5MB

                for (let file of this.files) {
                    if (file.size > maxSize) {
                        alert(`حجم الملف ${file.name} كبير جداً. الحد الأقصى المسموح هو 5MB`);
                        this.value = '';
                        return;
                    }
                }
            });
        });
    </script>
</body>
</html>
